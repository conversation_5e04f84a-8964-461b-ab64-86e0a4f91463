package app

import (
	"context"
	"slices"
	"time"

	"billing_service/model"

	null "github.com/guregu/null/v6"
)

func (a *App) GetClientPaymentTypes(ctx context.Context, clientId int) (resp model.ClientPaymentTypesResponse, err error) {
	resp.Cash = model.ClientCashPaymentType{
		PaymentTypeId:     model.PaymentTypeCash,
		IsAvailable:       true, // TODO get from db
		PromoAvailable:    true, // TODO get from db
		CashbackAvailable: true, // TODO get from db
	}

	resp.Click = model.ClickPaymentType{
		PaymentTypeId: model.PaymentTypeClick,
		IsAvailable:   true,
	}

	corp, err := a.repo.GetCorpClientBalanceInfo(ctx, clientId)
	if err != nil {
		return
	}

	if corp.IsCorporate {
		resp.Corp = &model.ClientCorpPaymentType{
			PaymentTypeId: model.PaymentTypeCorp,
			IsAvailable:   corp.IsAvailable,
			Balance:       corp.Balance,
		}
	}

	cards, err := a.GetCards(ctx, clientId, "client")
	if err != nil {
		return
	}

	for _, card := range cards {
		c := model.ClientCardPaymentType{
			CardId:            card.Id,
			Number:            card.Number,
			Status:            card.Status,
			Brand:             card.Brand,
			CashbackAvailable: true,
			PromoAvailable:    true,
		}

		if c.Brand == "mastercard" && a.repo.GetSysParam("discount_enabled", "0").Bool() && card.AtmosToken.String != "" {
			c.CashbackAvailable = false
			c.PromoAvailable = false
			switch card.Category.String {
			case "standart":
				c.Discount = &model.Discount{
					Name:  "mastercard discount",
					Type:  "percent",
					Value: 20,
					Limit: a.repo.GetSysParam("discount_limit", "300000").Int(),
				}
			case "premium":
				c.Discount = &model.Discount{
					Name:  "mastercard discount",
					Type:  "percent",
					Value: 30,
					Limit: a.repo.GetSysParam("discount_limit", "300000").Int(),
				}
			}
		}

		if card.PaymeToken.String != "" {
			c.PaymentTypeId = model.PaymentTypePayme
		} else if card.AtmosToken.String != "" {
			c.PaymentTypeId = model.PaymentTypeAtmos
		} else {
			c.PaymentTypeId = model.PaymentTypePayme
		}

		resp.Cards = append(resp.Cards, c)
	}

	cl, err := a.repo.GetClientInfo(ctx, clientId)
	if err != nil {
		a.log.Errorf("get client %d info: %v", clientId, err)
	} else {
		if cl.IsCashbackEnabled {
			resp.Cashback.IsAvailable = true
			var cashback model.CorpClientBalanceResponse
			cashback, err = a.repo.GetClientCashback(ctx, clientId)
			if err != nil {
				a.log.Errorf("get client %d cashback: %v", clientId, err)
			} else {
				resp.Cashback.Balance = cashback.Balance
			}
		}
		var isNewClient bool
		if cl.RideCount == 0 {
			isNewClient = true
		}

		resp.Promocodes, err = a.repo.GetPromocodes(ctx, clientId, isNewClient)
		if err != nil {
			a.log.Errorf("get client %d promocodes: %v", clientId, err)
		}

		if len(resp.Promocodes) > 0 {
			for i := 0; i < len(resp.Promocodes); i++ {
				code := resp.Promocodes[i].Code
				var promo model.PromocodeInfo
				promo, err = a.repo.GetPromocodeInfo(ctx, clientId, code)
				if err != nil {
					a.log.Errorf("get promocode %s info: %v", code, err)
				} else if promo.IsActive {
					if promo.UsedTimes < promo.EachClientUseTimes && promo.Value > promo.Usage {
						validDays := resp.Promocodes[i].ValidDays.Int64
						if validDays > 0 {
							expireTime := cl.RegTime.Add(time.Duration(24*validDays) * time.Hour)
							if time.Now().After(expireTime) {
								resp.Promocodes = slices.Delete(resp.Promocodes, i, i+1)
								i--
							} else {
								resp.Promocodes[i].Limit = promo.EachClientUseTimes - promo.UsedTimes
								resp.Promocodes[i].Value = promo.Value - promo.Usage
								resp.Promocodes[i].ExpireTime = null.TimeFrom(expireTime)
							}
						} else {
							resp.Promocodes[i].Limit = promo.EachClientUseTimes - promo.UsedTimes
							resp.Promocodes[i].Value = promo.Value - promo.Usage
						}
					} else {
						resp.Promocodes = slices.Delete(resp.Promocodes, i, i+1)
						i--
					}
				} else {
					err = a.repo.XpanelUpdatePromocode(ctx, code, null.Int{}, null.BoolFrom(false))
					if err != nil {
						a.log.Errorf("update promocode: %v", err)
					}
					resp.Promocodes = slices.Delete(resp.Promocodes, i, i+1)
					i--
				}
			}
		}
	}

	resp.Debts, err = a.repo.GetDebts(ctx, clientId, "client")

	return
}

func (a *App) GetClientCashback(ctx context.Context, clientId int) (resp model.ClientCashback, err error) {
	cl, err := a.repo.GetClientInfo(ctx, clientId)
	if err != nil {
		return
	}

	if cl.IsCashbackEnabled {
		resp.IsEnabled = true
		var cashback model.CorpClientBalanceResponse
		cashback, err = a.repo.GetClientCashback(ctx, clientId)
		resp.Balance = cashback.Balance
		resp.IsAvailable = cashback.Available
	}

	return
}

func (a *App) GetClientCashbackOld(ctx context.Context, clientId int) (resp model.ClientCashback, err error) {
	cl, err := a.repo.GetClientInfo(ctx, clientId)
	if err != nil {
		return
	}

	resp.IsAvailable = true

	if cl.IsCashbackEnabled {
		resp.IsEnabled = true
		var cashback model.CorpClientBalanceResponse
		cashback, err = a.repo.GetClientCashback(ctx, clientId)
		resp.Balance = cashback.Balance
	}

	return
}

func (a *App) GetClientPaymentTypesOLd(ctx context.Context, clientId int, lang string) (resp []model.ClientPaymentTypeOLd, err error) {
	cashType := model.ClientPaymentTypeOLd{
		Id:          model.PaymentTypeCash,
		Alias:       "cash",
		IsAvailable: true,
		IsVerified:  true,
		IsServiceOk: true,
	}

	corpType := model.ClientPaymentTypeOLd{
		Id:          model.PaymentTypeCorp,
		Alias:       "corporate_account",
		IsServiceOk: true,
	}

	paymeType := model.ClientPaymentTypeOLd{
		Id:          model.PaymentTypePayme,
		Alias:       "payme",
		Name:        "Payme",
		IsAvailable: true,
		IsVerified:  true,
		IsServiceOk: true,
	}

	visaType := model.ClientPaymentTypeOLd{
		Id:    model.PaymentTypeAtmos,
		Alias: "visa",
		Name:  "Visa",
	}

	if !a.repo.GetSysParam("is_payme_enabled", "0").Bool() {
		paymeType.IsServiceOk = false
	}

	if a.repo.GetSysParam("is_atmos_enabled", "0").Bool() {
		visaType.IsAvailable = true
		visaType.IsVerified = true
		visaType.IsServiceOk = true
	}

	corp, err := a.repo.GetCorpClientBalanceInfo(ctx, clientId)
	if err != nil {
		a.log.Error(err)
	} else if corp.IsCorporate {
		corpType.IsVerified = true
		if !corp.IsCompanyBlocked && corp.IsAvailable {
			corpType.IsAvailable = true
		}
	}

	switch lang {
	case "ru":
		cashType.Name = "Наличными"
		corpType.Name = "Корпоративный"
	case "uz":
		cashType.Name = "Naqd pul"
		corpType.Name = "Korporativ"
	case "en":
		cashType.Name = "Cash"
		corpType.Name = "Corporate"
	}

	resp = []model.ClientPaymentTypeOLd{cashType, paymeType, corpType, visaType}

	return
}
