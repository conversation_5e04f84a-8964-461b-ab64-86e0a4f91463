package api

import (
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"billing_service/util/provider/atmos"
	"billing_service/util/provider/click"
	"billing_service/util/provider/payme"
	"billing_service/util/provider/paynet"

	fiber "github.com/gofiber/fiber/v3"
)

func (h *handler) PaymeDriverWebhook(c fiber.Ctx) error {
	var (
		req  payme.WebhookRequest
		resp payme.WebhookResponse
	)

	h.log.Info("driver payme webhook: " + string(c.Body()))

	password := string(c.Request().Header.Peek("Authorization"))
	if password == "" {
		resp.SetPermissionDenied()
		return c.Status(http.StatusUnauthorized).JSON(resp)
	}

	parts := strings.Fields(password)
	if len(parts) == 0 {
		resp.SetPermissionDenied()
		return c.Status(http.StatusUnauthorized).JSON(resp)
	}

	encodedCredentials := parts[len(parts)-1]

	decodedBytes, err := base64.StdEncoding.DecodeString(encodedCredentials)
	if err != nil {
		resp.SetPermissionDenied()
		return c.Status(http.StatusUnauthorized).JSON(resp)
	}
	credentials := string(decodedBytes)

	credParts := strings.Split(credentials, ":")
	if len(credParts) == 0 {
		resp.SetPermissionDenied()
		return c.Status(http.StatusUnauthorized).JSON(resp)
	}

	merchantKey := credParts[len(credParts)-1]

	if merchantKey != h.cfg.Payme.DriverKey {
		resp.SetPermissionDenied()
		return c.Status(http.StatusUnauthorized).JSON(resp)
	}

	err = c.Bind().JSON(&req)
	if err != nil {
		resp.SetRPCRequestError()
		return c.Status(http.StatusBadRequest).JSON(resp)
	}

	resp, err = h.app.PaymeDriverWebhook(c.Context(), req)
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(resp)
	}

	resp.Id = req.Id

	// h.log.Infof("driver payme webhook response: %v", resp.Error)

	return c.JSON(resp)
}

func (h *handler) PaymeClientWebhook(c fiber.Ctx) error {
	var (
		req  payme.WebhookRequest
		resp payme.WebhookResponse
	)

	h.log.Info("client payme webhook: " + string(c.Body()))

	password := string(c.Request().Header.Peek("Authorization"))
	if password == "" {
		resp.SetPermissionDenied()
		return c.Status(http.StatusUnauthorized).JSON(resp)
	}

	parts := strings.Fields(password)
	if len(parts) == 0 {
		resp.SetPermissionDenied()
		return c.Status(http.StatusUnauthorized).JSON(resp)
	}

	encodedCredentials := parts[len(parts)-1]

	decodedBytes, err := base64.StdEncoding.DecodeString(encodedCredentials)
	if err != nil {
		resp.SetPermissionDenied()
		return c.Status(http.StatusUnauthorized).JSON(resp)
	}
	credentials := string(decodedBytes)

	credParts := strings.Split(credentials, ":")
	if len(credParts) == 0 {
		resp.SetPermissionDenied()
		return c.Status(http.StatusUnauthorized).JSON(resp)
	}

	merchantKey := credParts[len(credParts)-1]

	if merchantKey != h.cfg.Payme.Key {
		resp.SetPermissionDenied()
		return c.Status(http.StatusUnauthorized).JSON(resp)
	}

	err = c.Bind().JSON(&req)
	if err != nil {
		resp.SetRPCRequestError()
		return c.Status(http.StatusBadRequest).JSON(resp)
	}

	resp, err = h.app.PaymeClientWebhook(c.Context(), req)
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(resp)
	}

	resp.Id = req.Id

	// h.log.Infof("client payme webhook response: %v", resp.Error)

	return c.JSON(resp)
}

func (h *handler) PaynetWebhook(c fiber.Ctx) error {
	var req paynet.WehookRequest

	h.log.Info("paynet webhook: " + string(c.Body()))

	authorization := string(c.Request().Header.Peek("Authorization"))

	if !h.authenticatePaynetRequest(authorization) {
		var resp paynet.WebhookResponse
		resp.SetInvalidLoginOrPassword()
		return c.Status(http.StatusUnauthorized).JSON(resp)
	}

	err := c.Bind().JSON(&req)
	if err != nil {
		var resp paynet.WebhookResponse
		resp.SetJSONParsingError(err.Error())
		return c.Status(http.StatusBadRequest).JSON(resp)
	}

	resp := h.app.PaynetWebhook(c.Context(), req)

	return c.JSON(resp)
}

func (h *handler) authenticatePaynetRequest(creds string) bool {
	if creds == "" || !strings.HasPrefix(creds, "Basic ") {
		return false
	}

	encodedCreds := creds[6:]
	decodedBytes, err := base64.StdEncoding.DecodeString(encodedCreds)
	if err != nil {
		return false
	}

	decodedString := string(decodedBytes)
	parts := strings.SplitN(decodedString, ":", 2)
	if len(parts) != 2 {
		return false
	}

	// TODO: remove
	return parts[0] == h.cfg.Paynet.Username && parts[1] == h.cfg.Paynet.Password
}

func (h *handler) ClickWebhook(c fiber.Ctx) error {
	req, err := parseClickWebhookRequest(c)

	if err != nil {
		return h.badRequestResponse(c, err.Error())
	}

	h.log.Info(fmt.Sprintf("Received Click webhook click_trans_id=%s action=%d",
		req.ClickTransID, req.Action))

	if err := h.authenticateClickRequest(req, "", ""); err != nil {
		return h.badRequestResponse(c, err.Error())
	}

	resp := h.app.ClickWebHook(c.Context(), req)

	return c.JSON(resp)
}

func (h *handler) authenticateClickRequest(req click.WebhookRequest, serviceID, secretKey string) error {
	if serviceID == "" {
		serviceID = h.cfg.Click.ServiceID
	}
	if secretKey == "" {
		secretKey = h.cfg.Click.SecretKey
	}

	if serviceID == "" || secretKey == "" {
		return fmt.Errorf("missing required Click settings: service_id or secret_key")
	}

	merchantPrepareID := ""
	if req.MerchantPrepareID != "" {
		merchantPrepareID = req.MerchantPrepareID
	}

	textParts := []string{
		req.ClickTransID,
		serviceID,
		secretKey,
		req.MerchantTransID,
		merchantPrepareID,
		req.Amount,
		strconv.Itoa(req.Action),
		req.SignTime,
	}
	text := strings.Join(textParts, "")

	hash := md5.Sum([]byte(text))
	calculatedHash := hex.EncodeToString(hash[:])

	if calculatedHash != req.SignString {
		return fmt.Errorf("invalid signature")
	}

	return nil
}

func parseClickWebhookRequest(c fiber.Ctx) (click.WebhookRequest, error) {
	action, err := strconv.Atoi(c.FormValue("action"))

	if err != nil {
		return click.WebhookRequest{}, fmt.Errorf("invalid action value")
	}

	req := click.WebhookRequest{
		ClickTransID:    c.FormValue("click_trans_id"),
		ServiceID:       c.FormValue("service_id"),
		ClickPaydocID:   c.FormValue("click_paydoc_id"),
		MerchantTransID: c.FormValue("merchant_trans_id"),
		Amount:          c.FormValue("amount"),
		Action:          action,
		SignTime:        c.FormValue("sign_time"),
		Error:           c.FormValue("error"),
		ErrorNote:       c.FormValue("error_note"),
		SignString:      c.FormValue("sign_string"),
	}

	// printing values with keys
	// for debugging purposes
	fmt.Printf("Click Webhook Request: %+v\n", req)

	if action == click.ClickCompleteAction {
		req.MerchantPrepareID = c.FormValue("merchant_prepare_id")
	}

	return req, nil
}

func (h *handler) AtmosWebhook(c fiber.Ctx) error {
	h.log.Info("atmos webhook: " + strings.ReplaceAll(string(c.Body()), "\n", ""))

	var req atmos.WebhookRequest

	err := c.Bind().JSON(&req)
	if err != nil {
		return c.Status(http.StatusBadRequest).JSON(map[string]string{"message": err.Error()})
	}

	if req.CardID == "" {
		return c.Status(http.StatusBadRequest).JSON(map[string]string{"message": "card_id is required"})
	}

	err = h.app.AtmosWebhook(c.Context(), req)
	if err != nil {
		return c.Status(http.StatusInternalServerError).JSON(map[string]string{"message": err.Error()})
	}

	response := fiber.Map{
		"status":  1,
		"message": "Успешно",
	}

	return c.JSON(response)
}
