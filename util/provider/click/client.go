package click

import (
	"bytes"
	"context"
	"crypto/sha1"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

const (
	defaultTimeout      = 2 * time.Hour
	baseURL             = "https://api.click.uz"
	holdCreateEndpoint  = "/v2/merchant/hold/create"
	holdConfirmEndpoint = "/v2/merchant/hold/confirm"
	holdCancelEndpoint  = "/v2/merchant/hold/cancel"
	holdStatusEndpoint  = "/v2/merchant/hold/status"
	maxLogBodyLength    = 500
)

type Client struct {
	MerchantID     string
	ServiceID      string
	SecretKey      string
	MerchantUserId string
	httpClient     *http.Client
}

func NewClient(merchantID, serviceID, secretKey string, merchantUserId string) *Client {
	return &Client{
		MerchantID:     merchantID,
		ServiceID:      serviceID,
		SecretKey:      secretKey,
		MerchantUserId: merchantUserId,
		httpClient:     &http.Client{Timeout: defaultTimeout},
	}
}

func (c *Client) generateAuthHeader() string {
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	digestInput := timestamp + c.SecretKey

	hash := sha1.New()
	hash.Write([]byte(digestInput))
	digest := fmt.Sprintf("%x", hash.Sum(nil))

	return fmt.Sprintf("%s:%s:%s", c.MerchantUserId, digest, timestamp)
}

func (c *Client) sendRequest(ctx context.Context, endpoint, method string, body any) ([]byte, error) {
	if body == nil {
		return nil, errors.New("request body is nil")
	}

	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %w", err)
	}

	fullURL := baseURL + endpoint
	req, err := http.NewRequestWithContext(ctx, method, fullURL, bytes.NewReader(bodyBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Auth", c.generateAuthHeader())

	c.logRequest(method, endpoint, body)

	resp, err := c.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	c.logResponse(method, endpoint, resp.StatusCode, respBody)

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("request failed (status %d): %s", resp.StatusCode, string(respBody))
	}

	return respBody, nil
}

func (c *Client) logRequest(method, endpoint string, body any) {
	if body != nil {
		if prettyBody, err := json.MarshalIndent(body, "", "  "); err == nil {
			log.Printf("Request to %s %s:\n%s", method, endpoint, prettyBody)
		} else {
			log.Printf("Request to %s %s: <failed to marshal body>", method, endpoint)
		}
	} else {
		log.Printf("Request to %s %s: <no body>", method, endpoint)
	}
}

func (c *Client) logResponse(method, endpoint string, statusCode int, respBody []byte) {
	if len(respBody) == 0 {
		log.Printf("Response from %s %s [%d]: <empty body>", method, endpoint, statusCode)
		return
	}

	if json.Valid(respBody) {
		var prettyResp bytes.Buffer
		if err := json.Indent(&prettyResp, respBody, "", "  "); err == nil {
			log.Printf("Response from %s %s [%d]:\n%s", method, endpoint, statusCode, prettyResp.String())
		} else {
			log.Printf("Response from %s %s [%d]: <failed to format JSON>", method, endpoint, statusCode)
		}
	} else {
		body := string(respBody)
		if len(body) > maxLogBodyLength {
			body = body[:maxLogBodyLength] + "... (truncated)"
		}
		log.Printf("Response from %s %s [%d]: %s", method, endpoint, statusCode, body)
	}
}

func (c *Client) CreateHoldWithContext(ctx context.Context, phoneNumber, externalID string, amount, timeSeconds int) (*HoldCreateResponse, error) {
	serviceID, err := strconv.Atoi(c.ServiceID)
	if err != nil {
		return nil, fmt.Errorf("invalid service_id: %v", err)
	}

	request := HoldCreateRequest{
		ServiceID:   serviceID,
		PhoneNumber: phoneNumber,
		Amount:      amount,
		ExternalID:  externalID,
		Time:        timeSeconds,
	}

	respBody, err := c.sendRequest(ctx, holdCreateEndpoint, http.MethodPost, request)
	if err != nil {
		return nil, fmt.Errorf("hold create request: %v", err)
	}

	var response HoldCreateResponse
	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("parse hold create response: %v", err)
	}

	return &response, nil
}

func (c *Client) ConfirmHoldWithContext(ctx context.Context, paymentID int64, amount int) (*HoldConfirmResponse, error) {
	serviceID, err := strconv.Atoi(c.ServiceID)
	if err != nil {
		return nil, fmt.Errorf("invalid service_id: %v", err)
	}

	request := HoldConfirmRequest{
		Amount:    amount,
		PaymentID: paymentID,
		ServiceID: serviceID,
	}

	respBody, err := c.sendRequest(ctx, holdConfirmEndpoint, http.MethodPost, request)
	if err != nil {
		return nil, fmt.Errorf("hold confirm request: %v", err)
	}

	var response HoldConfirmResponse
	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("parse hold confirm response: %v", err)
	}

	return &response, nil
}

func (c *Client) CancelHoldWithContext(ctx context.Context, paymentID int64) (*HoldCancelResponse, error) {
	serviceID, err := strconv.Atoi(c.ServiceID)
	if err != nil {
		return nil, fmt.Errorf("invalid service_id: %v", err)
	}

	request := HoldCancelRequest{
		PaymentID: paymentID,
		ServiceID: serviceID,
	}

	respBody, err := c.sendRequest(ctx, holdCancelEndpoint, http.MethodPost, request)
	if err != nil {
		return nil, fmt.Errorf("hold cancel request: %v", err)
	}

	var response HoldCancelResponse
	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("parse hold cancel response: %v", err)
	}

	return &response, nil
}

func (c *Client) GetHoldStatusWithContext(ctx context.Context, paymentID int64) (*HoldStatusResponse, error) {
	endpoint := fmt.Sprintf("%s/%s/%d", holdStatusEndpoint, c.ServiceID, paymentID)

	respBody, err := c.sendRequest(ctx, endpoint, http.MethodGet, nil)
	if err != nil {
		return nil, fmt.Errorf("hold status request: %v", err)
	}

	var response HoldStatusResponse
	if err := json.Unmarshal(respBody, &response); err != nil {
		return nil, fmt.Errorf("parse hold status response: %v", err)
	}

	return &response, nil
}

// Convenience methods without context
func (c *Client) CreateHold(phoneNumber, externalID string, amount, timeSeconds int) (*HoldCreateResponse, error) {
	return c.CreateHoldWithContext(context.Background(), phoneNumber, externalID, amount, timeSeconds)
}

func (c *Client) ConfirmHold(paymentID int64, amount int) (*HoldConfirmResponse, error) {
	return c.ConfirmHoldWithContext(context.Background(), paymentID, amount)
}

func (c *Client) CancelHold(paymentID int64) (*HoldCancelResponse, error) {
	return c.CancelHoldWithContext(context.Background(), paymentID)
}

func (c *Client) GetHoldStatus(paymentID int64) (*HoldStatusResponse, error) {
	return c.GetHoldStatusWithContext(context.Background(), paymentID)
}

func (c *Client) GenerateSuperAppPayment(amount, transactionID int, auth bool, hideCross bool, returnPage string) string {

	deepLink := fmt.Sprintf("https://my.click.uz/app/webView?auth=%t&hide_cross=%t&url=", auth, hideCross)

	encodedDeepLinkWithPage := deepLink + url.QueryEscape(returnPage)

	encodedFinalDeepLink := url.QueryEscape(encodedDeepLinkWithPage)

	// Create the final URL
	finalURL := fmt.Sprintf(
		"https://my.click.uz/services/pay/?service_id=%s&merchant_id=%s&amount=%d&transaction_param=%d&return_url=%s",
		c.ServiceID,
		c.MerchantID,
		amount,
		transactionID,
		encodedFinalDeepLink,
	)

	return finalURL
}
